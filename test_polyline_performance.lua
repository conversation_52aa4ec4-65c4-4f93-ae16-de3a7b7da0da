-- Polyline Performance Test Script
-- Tests the wire-based sweep optimization for polyline groove operations

print("=== Polyline Performance Test ===")
print("Testing wire-based sweep optimization vs overlapping cylinders")

-- Initialize ADekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for performance test
AdekoLib.makePart(300, 250)
print("1. Created test panel: 300x250mm")

-- Test 1: Simple polyline groove (baseline)
print("\n2. Creating simple polyline groove...")
AdekoLib.layer("K_6MM_SIMPLE")  -- K_ prefix = groove operation
AdekoLib.polyline(
    {50, 50}, {100, 50}, {100, 100}, {50, 100}, {50, 50}
)
print("   - Simple 4-segment rectangle")

-- Test 2: Complex spiral groove (performance test)
print("\n3. Creating complex spiral groove...")
AdekoLib.layer("K_6MM_SPIRAL")  -- K_ prefix = groove operation
AdekoLib.polyline(
    {60, 60},   {80, 60},   {80, 80},   {60, 80},   {60, 100},  {100, 100},
    {100, 60},  {120, 60},  {120, 120}, {60, 120},  {60, 140},  {140, 140},
    {140, 60},  {160, 60},  {160, 160}, {60, 160},  {60, 180},  {180, 180},
    {180, 60},  {200, 60},  {200, 200}, {60, 200},  {60, 220},  {220, 220},
    {220, 60},  {240, 60},  {240, 240}, {60, 240}
)
print("   - Complex 27-segment spiral")
print("   - This tests the wire-based sweep optimization")

-- Test 3: Very complex polyline (stress test)
print("\n4. Creating stress test polyline...")
AdekoLib.layer("K_4MM_STRESS")  -- K_ prefix = groove operation

-- Generate a complex zigzag pattern
local points = {}
for i = 0, 20 do
    local x = 50 + i * 8
    local y = 50 + (i % 2) * 30 + math.sin(i * 0.5) * 10
    table.insert(points, {x, y})
end

-- Add the points to create the polyline
AdekoLib.polyline(table.unpack(points))
print("   - Stress test: 21-segment zigzag with curves")

print("\n5. Test geometry created successfully!")
print("   - Simple groove: 4 segments")
print("   - Spiral groove: 27 segments") 
print("   - Stress test: 21 segments")
print("   - Total: 52 segments across 3 polylines")

-- Finish the model
AdekoLib.finish()

print("\n=== Performance Test Instructions ===")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the 'Polyline Performance' test")
print("4. Check console for timing information:")
print("   - Look for 'Created swept groove shape following polyline path in XXXms'")
print("   - Compare times for different complexity levels")
print("   - Wire-based approach should be significantly faster")

print("\n=== Expected Results ===")
print("Wire-based sweep optimization should show:")
print("- Continuous smooth grooves (not segmented)")
print("- Fast processing times (< 100ms per polyline)")
print("- Linear scaling with segment count")
print("- Reduced memory usage")
print("- High geometric accuracy")

print("\n=== Technical Details ===")
print("The optimization uses:")
print("- BRepBuilderAPI_MakeEdge for edge creation")
print("- BRepBuilderAPI_MakeWire for wire assembly")
print("- gp_Circ for circular tool profile")
print("- BRepOffsetAPI_MakePipe for sweep operation")
print("- Single sweep instead of hundreds of boolean fusions")

print("\n=== Comparison with Old Method ===")
print("Old overlapping cylinders method:")
print("- Creates ~10-20 cylinders per segment")
print("- Performs ~10-20 boolean fusions per segment")
print("- For 27 segments: ~270-540 operations")
print("- Exponential performance degradation")
print("")
print("New wire-based method:")
print("- Creates 1 edge per segment")
print("- Performs 1 sweep operation total")
print("- For 27 segments: 28 operations")
print("- Linear performance scaling")

print("\n✅ Polyline Performance Test Complete")
print("Run the test to see the optimization in action!")
