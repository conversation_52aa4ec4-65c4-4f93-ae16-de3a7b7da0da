-- Test Rectangle Operations Fix
-- This script tests the corrected rectangle operations

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(250, 200, 20)

print("=== Testing Fixed Rectangle Operations ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 250, 200)

-- Test 2: Rectangle operations (FIXED - Different Operations)
print("2. Creating rectangle operations with different layer names...")

-- Rectangular pocket operation with 12MM tool
print("   Creating rectangular POCKET (150x50mm)...")
AdekoLib.layer("CEP_12MM_POCKET")  -- CEP prefix = pocket operation
AdekoLib.rect("rect_pocket", 50, 50, 200, 100)  -- 150x50mm rectangle

-- Rectangular groove operation with 8MM tool
print("   Creating rectangular GROOVE (90x50mm)...")
AdekoLib.layer("K_8MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.rect("rect_groove", 30, 120, 120, 170)  -- 90x50mm rectangle

-- Rectangular drilling operation with 6MM tool
print("   Creating rectangular DRILLING (70x50mm)...")
AdekoLib.layer("DRILL_6MM")  -- DRILL prefix = drilling operation
AdekoLib.rect("rect_drill", 150, 120, 220, 170)  -- 70x50mm rectangle

print("3. Test geometry created successfully!")
print("   - Door panel: 250x200x20mm")
print("   - Rectangle 1: 150x50mm POCKET (CEP_12MM_POCKET)")
print("   - Rectangle 2: 90x50mm GROOVE (K_8MM_GROOVE)")
print("   - Rectangle 3: 70x50mm DRILLING (DRILL_6MM)")
print("   - Ready for OCJS rectangle processing with different operations")

-- Finish the model
AdekoLib.finish()

print("\n=== Rectangle Operations Fix Complete ===")
print("This script tests the corrected rectangle operations:")
print("1. Uses proper rectangle command type instead of line")
print("2. Uses different layer names to trigger different operations")
print("3. Each rectangle should show different behavior based on layer name")
print("\nExpected results:")
print("- Rectangle 1: POCKET - Full rectangular material removal (solid box)")
print("- Rectangle 2: GROOVE - Rectangular outline/border (hollow rectangle)")
print("- Rectangle 3: DRILLING - Cylindrical hole at rectangle center")
print("- Three different operations, not three identical pockets!")
print("\nTo test:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the 'Rectangle Operations' test")
print("4. Verify 3D visualization shows rectangular cuts")
