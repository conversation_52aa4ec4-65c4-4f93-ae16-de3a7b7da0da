-- Simple Polyline Groove Test - Dotted Line Pattern
-- This script tests the simplified polyline groove that creates holes at each point

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(200, 150, 18)

print("=== Simple Polyline Groove Test - Dotted Line ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 200, 150)

-- Test 2: Simple polyline groove - should create holes at each point
print("2. Creating simple polyline groove...")
print("   This should create holes at each point, forming a dotted line pattern")

AdekoLib.layer("K_6MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("simple_groove", {
    {50, 50},    -- Point 1
    {100, 50},   -- Point 2 (horizontal line)
    {100, 100},  -- Point 3 (vertical line)
    {150, 100},  -- Point 4 (horizontal line)
    {150, 50}    -- Point 5 (vertical line) - creates a "U" shape
})

print("3. Test geometry created successfully!")
print("   - Door panel: 200x150x18mm")
print("   - Simple U-shaped polyline groove (K_6MM_GROOVE)")
print("   - Should create 5 holes following the U-shaped path")
print("   - NO rectangle - just holes at each point!")

-- Finish the model
AdekoLib.finish()

print("\n=== Simple Polyline Groove Test Complete ===")
print("This script tests the simplified polyline groove operation:")
print("1. Creates holes at each polyline point")
print("2. Forms a 'dotted line' pattern following the path")
print("3. Should clearly show the polyline path, not a rectangle")
print("\nExpected result:")
print("- 5 cylindrical holes at the specified points")
print("- Holes should form a U-shaped pattern")
print("- Should NOT be a rectangle or bounding box")
print("- Should clearly follow the polyline path")

print("\nU-shaped path details:")
print("- Point 1: (50, 50) - bottom left")
print("- Point 2: (100, 50) - bottom center")
print("- Point 3: (100, 100) - top center")
print("- Point 4: (150, 100) - top right")
print("- Point 5: (150, 50) - bottom right")
print("- Forms a clear U shape")

print("\nTo test:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the 'Polyline Operations' test")
print("4. Look for the U-shaped dotted line pattern")
print("5. Verify it's NOT a rectangle")
print("6. Check console for 'Created groove with 5 cylindrical points'")

print("\nWhat you should see:")
print("- 5 holes arranged in a U shape")
print("- Each hole should be at the exact coordinates specified")
print("- The pattern should clearly show the polyline path")
print("- NO rectangular pocket - just the dotted line pattern!")
print("- This proves the polyline groove follows the actual path")
