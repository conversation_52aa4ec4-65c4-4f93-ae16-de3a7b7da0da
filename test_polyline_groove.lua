-- Test <PERSON>yline Groove Operation
-- This script specifically tests the polyline groove operation to see actual path following

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(250, 200, 20)

print("=== Testing Polyline Groove Operation ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 250, 200)

-- Test 2: Different polyline groove patterns
print("2. Creating polyline groove operations...")

-- Polyline 1: Simple rectangular border groove
print("   Creating rectangular border groove...")
AdekoLib.layer("K_8MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("border_groove", {
    {20, 20}, {230, 20}, {230, 180}, {20, 180}, {20, 20}
})

-- Polyline 2: Zigzag pattern groove
print("   Creating zigzag pattern groove...")
AdekoLib.layer("K_6MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("zigzag_groove", {
    {30, 50}, {70, 80}, {110, 50}, {150, 80}, {190, 50}, {220, 80}
})

-- Polyline 3: Star pattern groove
print("   Creating star pattern groove...")
AdekoLib.layer("K_4MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("star_groove", {
    {125, 120}, {140, 100}, {160, 110}, {145, 130}, {155, 150}, {125, 140}, {95, 150}, {105, 130}, {90, 110}, {110, 100}, {125, 120}
})

-- Polyline 4: Curved path approximation groove
print("   Creating curved path groove...")
AdekoLib.layer("K_5MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("curve_groove", {
    {50, 160}, {60, 155}, {70, 150}, {80, 148}, {90, 150}, {100, 155}, {110, 160}, {120, 165}, {130, 160}
})

-- Polyline 5: Complex decorative pattern
print("   Creating complex decorative groove...")
AdekoLib.layer("K_3MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("decorative_groove", {
    {180, 120}, {190, 110}, {200, 120}, {210, 110}, {220, 120}, {210, 130}, {200, 140}, {190, 130}, {180, 120}
})

print("3. Test geometry created successfully!")
print("   - Door panel: 250x200x20mm")
print("   - 5 different polyline groove patterns")
print("   - Each should follow the actual polyline path, not create rectangles")
print("   - Tools: 8MM, 6MM, 4MM, 5MM, 3MM")

-- Finish the model
AdekoLib.finish()

print("\n=== Polyline Groove Test Complete ===")
print("This script tests the corrected polyline groove operations:")
print("1. Creates grooves that follow actual polyline paths")
print("2. Uses cylindrical segments along each line segment")
print("3. Combines segments to form continuous groove")
print("\nExpected results:")
print("- Border groove: Rectangular outline following border path")
print("- Zigzag groove: Zigzag pattern with cylindrical segments")  
print("- Star groove: Star-shaped groove following star path")
print("- Curve groove: Smooth curved groove approximation")
print("- Decorative groove: Complex decorative pattern")
print("- NO rectangles - should see actual path following!")

print("\nTo test:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the 'Polyline Operations' test")
print("4. Verify 3D visualization shows grooves following actual paths")
print("5. Check console for 'Creating groove following actual polyline path'")
print("6. Should see cylindrical segments along each line segment")

print("\nWhat you should see:")
print("- Border: Groove following rectangular border (not filled rectangle)")
print("- Zigzag: Groove following zigzag path (not bounding box)")
print("- Star: Groove following star outline (not filled star)")
print("- Curve: Groove following curved path")
print("- Decorative: Groove following complex pattern")
print("- Each groove should be a 'trail' following the polyline path!")
