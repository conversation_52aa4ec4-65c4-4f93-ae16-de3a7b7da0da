-- Connected <PERSON>yline Groove Test
-- This script tests the connected polyline groove that creates rectangular segments between points

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(200, 150, 18)

print("=== Connected Polyline Groove Test ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 200, 150)

-- Test 2: Connected polyline groove - should create connected rectangular segments
print("2. Creating connected polyline groove...")
print("   This should create rectangular segments between each pair of points")

AdekoLib.layer("K_6MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("connected_groove", {
    {50, 50},    -- Point 1
    {100, 50},   -- Point 2 (horizontal segment)
    {100, 100},  -- Point 3 (vertical segment)
    {150, 100},  -- Point 4 (horizontal segment)
    {150, 50}    -- Point 5 (vertical segment) - creates a connected "U" shape
})

print("3. Test geometry created successfully!")
print("   - Door panel: 200x150x18mm")
print("   - Connected U-shaped polyline groove (K_6MM_GROOVE)")
print("   - Should create 4 rectangular segments connecting the 5 points")
print("   - Should form a continuous connected groove!")

-- Finish the model
AdekoLib.finish()

print("\n=== Connected Polyline Groove Test Complete ===")
print("This script tests the connected polyline groove operation:")
print("1. Creates rectangular segments between consecutive points")
print("2. Forms a continuous connected groove following the path")
print("3. Should clearly show the polyline path as a connected groove")
print("\nExpected result:")
print("- 4 rectangular groove segments connecting the points")
print("- Segments should form a continuous U-shaped groove")
print("- Should NOT be individual holes or a rectangle")
print("- Should be a connected path following the polyline")

print("\nConnected U-shaped path details:")
print("- Segment 1: (50,50) → (100,50) - horizontal bottom")
print("- Segment 2: (100,50) → (100,100) - vertical right")
print("- Segment 3: (100,100) → (150,100) - horizontal top")
print("- Segment 4: (150,100) → (150,50) - vertical left")
print("- Forms a connected U-shaped groove")

print("\nTo test:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the 'Polyline Operations' test")
print("4. Look for the connected U-shaped groove")
print("5. Verify it's connected segments, not individual holes")
print("6. Check console for 'Created connected groove with 4 segments'")

print("\nWhat you should see:")
print("- 4 rectangular groove segments")
print("- Segments should connect to form a continuous U shape")
print("- Each segment should be positioned between consecutive points")
print("- The groove should clearly follow the polyline path")
print("- NO individual holes - should be connected rectangular grooves!")
print("- This proves the polyline groove creates a connected path")
