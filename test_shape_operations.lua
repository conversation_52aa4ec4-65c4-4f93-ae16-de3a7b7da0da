-- Test Shape Operations with Cylindrical Tools
-- This script demonstrates various shape operations using cylindrical tools
-- Based on circle command examples, extended to other shapes

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(300, 250, 20)

print("=== Testing Shape Operations with Cylindrical Tools ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 300, 250)

-- Test 2: Circle operations (existing - as reference)
print("2. Creating circle operations...")

-- Circle drilling operation (no radius = drilling)
AdekoLib.layer("8MM_DRILL")
AdekoLib.circle("drill_hole", 50, 50, 0)  -- No radius = drilling operation

-- Circle groove operation (with radius = groove around perimeter)
AdekoLib.layer("K_6MM_GROOVE")  -- K_ prefix indicates groove operation
AdekoLib.circle("groove_circle", 150, 50, 15)  -- 15mm radius groove

-- Circle pocket operation (with radius = pocket entire area)
AdekoLib.layer("CEP_4MM_POCKET")  -- CEP prefix indicates pocket operation
AdekoLib.circle("pocket_circle", 250, 50, 20)  -- 20mm radius pocket

-- Test 3: Rectangle operations
print("3. Creating rectangle operations...")

-- Large rectangular pocket with 12MM tool
AdekoLib.layer("12MM")
AdekoLib.rect("large_pocket", 50, 100, 200, 150)  -- 150x50mm rectangle

-- Medium rectangular groove with 8MM tool
AdekoLib.layer("8MM")
AdekoLib.rect("medium_groove", 30, 120, 120, 170)  -- 90x50mm rectangle

-- Small rectangular profile with 6MM tool
AdekoLib.layer("6MM")
AdekoLib.rect("small_profile", 150, 120, 220, 170)  -- 70x50mm rectangle

-- Test 4: Line operations
print("4. Creating line operations...")

-- Horizontal line cut with 10MM tool
AdekoLib.layer("10MM")
AdekoLib.line("horizontal_cut", 20, 200, 120, 200)

-- Vertical line cut with 8MM tool
AdekoLib.layer("8MM")
AdekoLib.line("vertical_cut", 150, 180, 150, 230)

-- Diagonal line cut with 6MM tool
AdekoLib.layer("6MM")
AdekoLib.line("diagonal_cut", 180, 200, 280, 230)

-- Test 5: Polyline operations (complex paths)
print("5. Creating polyline operations...")

-- Decorative border with 8MM tool
AdekoLib.layer("8MM")
AdekoLib.polyline("decorative_border", {
    {10, 10}, {290, 10}, {290, 240}, {10, 240}, {10, 10}
})

-- Zigzag pattern with 6MM tool
AdekoLib.layer("6MM")
AdekoLib.polyline("zigzag_pattern", {
    {30, 70}, {60, 90}, {90, 70}, {120, 90}, {150, 70}
})

-- Star pattern with 4MM tool
AdekoLib.layer("4MM")
AdekoLib.polyline("star_pattern", {
    {200, 120}, {220, 100}, {240, 120}, {220, 140}, {200, 120}
})

-- Test 6: Arc operations (curved cuts)
print("6. Creating arc operations...")

-- Large semicircle arc with 10MM tool
AdekoLib.layer("10MM")
AdekoLib.arc("semicircle_arc", 75, 175, 30, 0, 180)

-- Quarter circle arc with 8MM tool
AdekoLib.layer("8MM")
AdekoLib.arc("quarter_arc", 200, 200, 20, 0, 90)

-- Three-quarter circle arc with 6MM tool
AdekoLib.layer("6MM")
AdekoLib.arc("threequarter_arc", 250, 175, 15, 45, 315)

print("7. Test geometry created successfully!")
print("   - Door panel: 300x250x20mm")
print("   - Circle operations: drilling (8MM), groove (6MM), pocket (4MM)")
print("   - Rectangle operations: large pocket (12MM), groove (8MM), profile (6MM)")
print("   - Line operations: horizontal (10MM), vertical (8MM), diagonal (6MM)")
print("   - Polyline operations: border (8MM), zigzag (6MM), star (4MM)")
print("   - Arc operations: semicircle (10MM), quarter (8MM), three-quarter (6MM)")
print("   - Ready for OCJS sweep processing")

-- Finish the model
AdekoLib.finish()

print("\n=== Shape Operations Test Complete ===")
print("This script creates various geometric shapes that can be used to test:")
print("1. Circle operations: drilling, grooving, pocketing")
print("2. Rectangle operations: pockets, grooves, profiles")
print("3. Line operations: horizontal, vertical, diagonal cuts")
print("4. Polyline operations: complex path following")
print("5. Arc operations: curved cuts and profiles")
print("6. All operations use cylindrical tools of different sizes")
print("\nTo test the shape operations:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the individual shape operation tests")
print("4. Check the 3D visualization for material removal")
print("5. Verify console output for operation details")

print("\nExpected results:")
print("- Circles: holes, grooves around perimeter, pocketed areas")
print("- Rectangles: rectangular pockets and profiles")
print("- Lines: straight cuts in different orientations")
print("- Polylines: complex decorative patterns")
print("- Arcs: curved cuts and decorative elements")
print("- All operations should show visible material removal")
