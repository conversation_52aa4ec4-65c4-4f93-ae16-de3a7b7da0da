-- Focused Polyline Groove Test
-- This script specifically tests the polyline groove operation with a clear spiral pattern

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(220, 180, 20)

print("=== Focused Polyline Groove Test ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 220, 180)

-- Test 2: Single polyline groove operation with spiral pattern
print("2. Creating spiral polyline groove...")
print("   This should create a groove following the spiral path, NOT a rectangle!")

AdekoLib.layer("K_6MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.polyline("spiral_groove", {
    {110, 90},   -- Start at center
    {130, 90},   -- Move right
    {130, 110},  -- Move down
    {90, 110},   -- Move left (wider)
    {90, 70},    -- Move up (wider)
    {150, 70},   -- Move right (even wider)
    {150, 130},  -- Move down (even wider)
    {70, 130},   -- Move left (widest)
    {70, 50}     -- End at top left
})

print("3. Test geometry created successfully!")
print("   - Door panel: 220x180x20mm")
print("   - Single spiral polyline groove (K_6MM_GROOVE)")
print("   - Pattern: Starts at center, spirals outward")
print("   - Should create groove following spiral path")

-- Finish the model
AdekoLib.finish()

print("\n=== Focused Polyline Groove Test Complete ===")
print("This script tests ONLY the polyline groove operation:")
print("1. Creates a single spiral polyline groove")
print("2. Uses K_6MM_GROOVE layer for groove operation")
print("3. Should follow the actual spiral path")
print("\nExpected result:")
print("- A groove that follows the spiral path from center outward")
print("- Should see cylindrical segments along each line segment")
print("- Should NOT be a rectangle or bounding box")
print("- Should be a continuous groove following the spiral")

print("\nSpiral path details:")
print("- Starts at center (110, 90)")
print("- Moves right to (130, 90)")
print("- Moves down to (130, 110)")
print("- Moves left wider to (90, 110)")
print("- Moves up wider to (90, 70)")
print("- Moves right even wider to (150, 70)")
print("- Moves down even wider to (150, 130)")
print("- Moves left widest to (70, 130)")
print("- Ends at top left (70, 50)")

print("\nTo test:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the 'Polyline Operations' test")
print("4. Look specifically at the spiral groove")
print("5. Verify it follows the spiral path, not a rectangle")
print("6. Check console for 'Creating groove following actual polyline path'")

print("\nWhat you should see:")
print("- A spiral-shaped groove carved into the door")
print("- The groove should start at the center and spiral outward")
print("- Each segment should be a cylindrical groove")
print("- The segments should be connected to form a continuous path")
print("- NO rectangular bounding box - just the spiral path!")
