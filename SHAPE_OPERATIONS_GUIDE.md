# Shape Operations with Cylindrical Tools Guide

This guide explains the various shape operations supported by the OCJS worker using cylindrical tools, based on the circle command example.

## Overview

The system now supports comprehensive shape operations with cylindrical tools:
- **Circle Operations**: Drilling, grooving, and pocketing
- **Rectangle Operations**: Rectangular pockets and profiles
- **Line Operations**: Linear cuts in various orientations
- **Polyline Operations**: Complex path following
- **Arc Operations**: Curved cuts and profiles

## Circle Operations (Reference Implementation)

Circle operations serve as the foundation for understanding how other shapes work:

### 1. Drilling Operation
```lua
AdekoLib.layer("8MM_DRILL")
AdekoLib.circle("drill_hole", 50, 50, 0)  -- No radius = drilling
```
- **Tool**: 8MM cylindrical
- **Radius**: 0 (no radius specified)
- **Operation**: Creates a hole at the center point
- **Result**: Material removal at exact center coordinates

### 2. Groove Operation
```lua
AdekoLib.layer("K_6MM_GROOVE")  -- K_ prefix indicates groove
AdekoLib.circle("groove_circle", 150, 50, 15)  -- 15mm radius groove
```
- **Tool**: 6MM cylindrical
- **Radius**: 15mm (groove around perimeter)
- **Operation**: Creates circular groove around the circle borderline
- **Result**: Ring-shaped material removal

### 3. Pocket Operation
```lua
AdekoLib.layer("CEP_4MM_POCKET")  -- CEP prefix indicates pocket
AdekoLib.circle("pocket_circle", 250, 50, 20)  -- 20mm radius pocket
```
- **Tool**: 4MM cylindrical
- **Radius**: 20mm (pocket entire area)
- **Operation**: Removes material from entire circle area
- **Result**: Circular pocket with spiral toolpath

## Rectangle Operations

Rectangle operations follow similar patterns to circles:

### 1. Large Rectangular Pocket
```lua
AdekoLib.layer("12MM")
AdekoLib.rect("large_pocket", 50, 100, 150, 150)
```
- **Tool**: 12MM cylindrical
- **Operation**: Creates rectangular pocket
- **Positioning**: Tool positioned at rectangle center
- **Result**: Rectangular material removal

### 2. Medium Rectangular Groove
```lua
AdekoLib.layer("8MM")
AdekoLib.rect("medium_groove", 180, 100, 250, 130)
```
- **Tool**: 8MM cylindrical
- **Operation**: Creates rectangular groove/profile
- **Result**: Rectangular outline cut

### 3. Small Rectangular Profile
```lua
AdekoLib.layer("6MM")
AdekoLib.rect("small_profile", 180, 150, 250, 180)
```
- **Tool**: 6MM cylindrical
- **Operation**: Creates precise rectangular profile
- **Result**: Clean rectangular cut

## Line Operations

Line operations create linear cuts with cylindrical tools:

### 1. Horizontal Line Cut
```lua
AdekoLib.layer("10MM")
AdekoLib.line("horizontal_cut", 20, 200, 120, 200)
```
- **Tool**: 10MM cylindrical
- **Direction**: Horizontal (Y coordinates same)
- **Positioning**: Tool at line midpoint
- **Result**: Straight horizontal groove

### 2. Vertical Line Cut
```lua
AdekoLib.layer("8MM")
AdekoLib.line("vertical_cut", 150, 180, 150, 230)
```
- **Tool**: 8MM cylindrical
- **Direction**: Vertical (X coordinates same)
- **Result**: Straight vertical groove

### 3. Diagonal Line Cut
```lua
AdekoLib.layer("6MM")
AdekoLib.line("diagonal_cut", 180, 200, 280, 230)
```
- **Tool**: 6MM cylindrical
- **Direction**: Diagonal (both X and Y change)
- **Result**: Angled groove

## Polyline Operations

Polyline operations handle complex paths with multiple connected segments:

### 1. Decorative Border
```lua
AdekoLib.layer("8MM")
AdekoLib.polyline("decorative_border", {
    {10, 10}, {290, 10}, {290, 240}, {10, 240}, {10, 10}
})
```
- **Tool**: 8MM cylindrical
- **Path**: Rectangular border (closed path)
- **Positioning**: Tool at path bounding box center
- **Result**: Decorative border groove

### 2. Zigzag Pattern
```lua
AdekoLib.layer("6MM")
AdekoLib.polyline("zigzag_pattern", {
    {30, 70}, {60, 90}, {90, 70}, {120, 90}, {150, 70}
})
```
- **Tool**: 6MM cylindrical
- **Path**: Zigzag pattern
- **Result**: Decorative zigzag groove

### 3. Star Pattern
```lua
AdekoLib.layer("4MM")
AdekoLib.polyline("star_pattern", {
    {200, 120}, {220, 100}, {240, 120}, {220, 140}, {200, 120}
})
```
- **Tool**: 4MM cylindrical
- **Path**: Star shape (closed path)
- **Result**: Star-shaped groove

## Arc Operations

Arc operations create curved cuts with cylindrical tools:

### 1. Semicircle Arc
```lua
AdekoLib.layer("10MM")
AdekoLib.arc("semicircle_arc", 75, 175, 30, 0, 180)
```
- **Tool**: 10MM cylindrical
- **Center**: (75, 175)
- **Radius**: 30mm
- **Angles**: 0° to 180° (semicircle)
- **Result**: Curved semicircular groove

### 2. Quarter Circle Arc
```lua
AdekoLib.layer("8MM")
AdekoLib.arc("quarter_arc", 200, 200, 20, 0, 90)
```
- **Tool**: 8MM cylindrical
- **Angles**: 0° to 90° (quarter circle)
- **Result**: Quarter-circle groove

### 3. Three-Quarter Circle Arc
```lua
AdekoLib.layer("6MM")
AdekoLib.arc("threequarter_arc", 250, 175, 15, 45, 315)
```
- **Tool**: 6MM cylindrical
- **Angles**: 45° to 315° (three-quarter circle)
- **Result**: Extended curved groove

## Tool Positioning Logic

All shape operations follow consistent positioning logic:

1. **Coordinate Conversion**: Lua coordinates (mm) → OpenCascade coordinates (meters)
2. **Door Centering**: Account for door offset and center relative to door size
3. **Surface Positioning**: Position tool to cut from surface into material
4. **Depth Control**: Tool height based on operation type and depth requirements

## Testing

Use the provided test files to verify shape operations:

1. **test_shape_operations.lua**: Comprehensive Lua test script
2. **test_shape_operations.html**: Interactive HTML test page
3. **SweepOperationsTestPage.vue**: Integrated Vue component tests

## Expected Results

Each shape operation should produce visible material removal:
- **Circles**: Holes, grooves, or pockets depending on radius
- **Rectangles**: Rectangular cuts and profiles
- **Lines**: Straight grooves in specified directions
- **Polylines**: Complex decorative patterns
- **Arcs**: Curved cuts and decorative elements

All operations use cylindrical tools and should show clean, precise material removal in the 3D visualization.
