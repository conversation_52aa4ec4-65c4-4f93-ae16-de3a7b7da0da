-- Simple Line Operations Test
-- This script tests the fixed line operations with different layer names

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(200, 150, 18)

print("=== Testing Simple Line Operations ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 200, 150)

-- Test 2: Line operations with different layer names
print("2. Creating line operations with different layer names...")

-- Line 1: Pocket operation (should create elongated rectangular pocket)
print("   Creating line POCKET operation...")
AdekoLib.layer("CEP_10MM_POCKET")  -- CEP prefix = pocket operation
AdekoLib.line("line_pocket", 20, 40, 180, 40)  -- Horizontal line

-- Line 2: Groove operation (should create elongated groove)
print("   Creating line GROOVE operation...")
AdekoLib.layer("K_8MM_GROOVE")  -- K_ prefix = groove operation
AdekoLib.line("line_groove", 100, 20, 100, 130)  -- Vertical line

-- Line 3: Drilling operation (should create hole at line center)
print("   Creating line DRILLING operation...")
AdekoLib.layer("DRILL_6MM")  -- DRILL prefix = drilling operation
AdekoLib.line("line_drill", 30, 80, 170, 120)  -- Diagonal line

print("3. Test geometry created successfully!")
print("   - Door panel: 200x150x18mm")
print("   - Line 1: Horizontal POCKET (CEP_10MM_POCKET)")
print("   - Line 2: Vertical GROOVE (K_8MM_GROOVE)")
print("   - Line 3: Diagonal DRILLING (DRILL_6MM)")
print("   - Ready for OCJS line processing with different operations")

-- Finish the model
AdekoLib.finish()

print("\n=== Simple Line Operations Test Complete ===")
print("This script tests the corrected line operations:")
print("1. Uses different layer names to trigger different operations")
print("2. Each line should show different behavior based on layer name")
print("\nExpected results:")
print("- Line 1: POCKET - Elongated rectangular material removal along line")
print("- Line 2: GROOVE - Elongated groove following line path")  
print("- Line 3: DRILLING - Cylindrical hole at line center (midpoint)")
print("- Three different operations, not three identical cuts!")

print("\nTo test:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run the 'Line Operations' test")
print("4. Verify 3D visualization shows different operation types")
print("5. Check console for operation type confirmations")
print("6. Should not get OpenCascade.js constructor errors")
