-- Test All Shape Operations with Different Layer-Based Operations
-- This script demonstrates all shapes with different operations based on layer names

-- Initialize AdekoLib
AdekoLib.start()
AdekoLib.showPoints(true)
AdekoLib.enableListing(true)

-- Set panel dimensions for testing
AdekoLib.setPanelSize(300, 250, 20)

print("=== Testing All Shape Operations with Different Layer Names ===")

-- Test 1: Create door panel
print("1. Creating door panel...")
AdekoLib.layer("PANEL")
AdekoLib.rect("door_panel", 0, 0, 300, 250)

-- Test 2: Circle operations (reference - already working correctly)
print("2. Creating circle operations...")
AdekoLib.layer("DRILL_8MM")  -- Drilling operation
AdekoLib.circle("circle_drill", 50, 50, 0)  -- No radius = drilling

AdekoLib.layer("K_6MM_GROOVE")  -- Groove operation
AdekoLib.circle("circle_groove", 150, 50, 15)  -- 15mm radius groove

AdekoLib.layer("CEP_4MM_POCKET")  -- Pocket operation
AdekoLib.circle("circle_pocket", 250, 50, 20)  -- 20mm radius pocket

-- Test 3: Rectangle operations (FIXED - Different Operations)
print("3. Creating rectangle operations with different layer names...")
AdekoLib.layer("CEP_12MM_POCKET")  -- Pocket operation
AdekoLib.rect("rect_pocket", 50, 100, 200, 150)  -- 150x50mm rectangle

AdekoLib.layer("K_8MM_GROOVE")  -- Groove operation
AdekoLib.rect("rect_groove", 30, 170, 120, 220)  -- 90x50mm rectangle

AdekoLib.layer("DRILL_6MM")  -- Drilling operation
AdekoLib.rect("rect_drill", 150, 170, 220, 220)  -- 70x50mm rectangle

-- Test 4: Line operations (FIXED - Different Operations)
print("4. Creating line operations with different layer names...")
AdekoLib.layer("CEP_10MM_POCKET")  -- Pocket operation
AdekoLib.line("line_pocket", 20, 40, 180, 40)  -- Horizontal line

AdekoLib.layer("K_8MM_GROOVE")  -- Groove operation
AdekoLib.line("line_groove", 100, 20, 100, 130)  -- Vertical line

AdekoLib.layer("DRILL_6MM")  -- Drilling operation
AdekoLib.line("line_drill", 30, 80, 170, 120)  -- Diagonal line

-- Test 5: Polyline operations (FIXED - Different Operations)
print("5. Creating polyline operations with different layer names...")
AdekoLib.layer("CEP_8MM_POCKET")  -- Pocket operation
AdekoLib.polyline("poly_pocket", {
    {10, 10}, {290, 10}, {290, 240}, {10, 240}, {10, 10}
})

AdekoLib.layer("K_6MM_GROOVE")  -- Groove operation
AdekoLib.polyline("poly_groove", {
    {30, 70}, {60, 90}, {90, 70}, {120, 90}, {150, 70}
})

AdekoLib.layer("DRILL_4MM")  -- Drilling operation
AdekoLib.polyline("poly_drill", {
    {200, 120}, {220, 100}, {240, 120}, {220, 140}, {200, 120}
})

-- Test 6: Arc operations (FIXED - Different Operations)
print("6. Creating arc operations with different layer names...")
AdekoLib.layer("CEP_10MM_POCKET")  -- Pocket operation
AdekoLib.arc("arc_pocket", 75, 175, 30, 0, 180)  -- Semicircle

AdekoLib.layer("K_8MM_GROOVE")  -- Groove operation
AdekoLib.arc("arc_groove", 200, 200, 20, 0, 90)  -- Quarter circle

AdekoLib.layer("DRILL_6MM")  -- Drilling operation
AdekoLib.arc("arc_drill", 250, 175, 15, 45, 315)  -- Three-quarter circle

print("7. Test geometry created successfully!")
print("   - Door panel: 300x250x20mm")
print("   - All shapes now use different layer names for different operations")
print("   - POCKET operations: CEP_ prefix")
print("   - GROOVE operations: K_ prefix")
print("   - DRILLING operations: DRILL_ prefix")

-- Finish the model
AdekoLib.finish()

print("\n=== All Shape Operations Fix Complete ===")
print("This script tests all shapes with corrected operation-based layer names:")
print("\n🔴 DRILLING Operations (DRILL_ prefix):")
print("  - Circle: Hole at center")
print("  - Rectangle: Hole at rectangle center")
print("  - Line: Hole at line midpoint")
print("  - Polyline: Hole at path center")
print("  - Arc: Hole at arc center")
print("\n🟡 GROOVE Operations (K_ prefix):")
print("  - Circle: Ring around perimeter")
print("  - Rectangle: Hollow rectangular frame")
print("  - Line: Elongated groove along line")
print("  - Polyline: Groove following path bounds")
print("  - Arc: Annular groove around radius")
print("\n🟢 POCKET Operations (CEP_ prefix):")
print("  - Circle: Full circular removal")
print("  - Rectangle: Full rectangular removal")
print("  - Line: Elongated rectangular pocket")
print("  - Polyline: Pocket covering path area")
print("  - Arc: Circular pocket at center")
print("\nExpected results:")
print("- Each shape type should show 3 different operations")
print("- No more identical cuts - each operation should be visually distinct")
print("- Total: 15 different operations across 5 shape types")

print("\nTo test:")
print("1. Run this script in the application")
print("2. Use the Sweep Operations Test Page")
print("3. Run individual shape operation tests")
print("4. Verify 3D visualization shows different operation types")
print("5. Check console for operation type confirmations")
